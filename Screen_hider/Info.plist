<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
    <!-- Basic Bundle Information -->
    <key>CFBundleDevelopmentRegion</key>
    <string>en</string>
    <key>CFBundleExecutable</key>
    <string>ScreenHider</string>
    <key>CFBundleIdentifier</key>
    <string>com.screenhider.app</string>
    <key>CFBundleInfoDictionaryVersion</key>
    <string>6.0</string>
    <key>CFBundleName</key>
    <string>ScreenHider</string>
    <key>CFBundleDisplayName</key>
    <string>ScreenHider</string>
    <key>CFBundlePackageType</key>
    <string>APPL</string>
    <key>CFBundleShortVersionString</key>
    <string>1.0.0</string>
    <key>CFBundleVersion</key>
    <string>1.0.0</string>

    <!-- Application Description -->
    <key>CFBundleGetInfoString</key>
    <string>ScreenHider 1.0.0 - Screen overlay for privacy during screen sharing</string>
    <key>NSHumanReadableCopyright</key>
    <string>Copyright © 2024 ScreenHider. All rights reserved.</string>

    <!-- System Requirements -->
    <key>LSMinimumSystemVersion</key>
    <string>11.0</string>
    <key>LSRequiresNativeExecution</key>
    <true/>

    <!-- Application Behavior -->
    <key>LSUIElement</key>
    <true/>
    <key>LSBackgroundOnly</key>
    <false/>
    <key>LSMultipleInstancesProhibited</key>
    <true/>

    <!-- Display and Graphics -->
    <key>NSHighResolutionCapable</key>
    <true/>
    <key>NSSupportsAutomaticGraphicsSwitching</key>
    <true/>
    <key>NSRequiresAquaSystemAppearance</key>
    <false/>

    <!-- Supported Architectures -->
    <key>LSArchitecturePriority</key>
    <array>
        <string>arm64</string>
        <string>x86_64</string>
    </array>

    <!-- Application Category -->
    <key>LSApplicationCategoryType</key>
    <string>public.app-category.utilities</string>

    <!-- Document Types (none for this app) -->
    <key>CFBundleDocumentTypes</key>
    <array/>

    <!-- URL Schemes (none for this app) -->
    <key>CFBundleURLTypes</key>
    <array/>

    <!-- Services (none for this app) -->
    <key>NSServices</key>
    <array/>

    <!-- Hardened Runtime and Security -->
    <key>com.apple.security.cs.allow-jit</key>
    <false/>
    <key>com.apple.security.cs.allow-unsigned-executable-memory</key>
    <false/>
    <key>com.apple.security.cs.allow-dyld-environment-variables</key>
    <false/>
    <key>com.apple.security.cs.disable-library-validation</key>
    <false/>

    <!-- App Transport Security -->
    <key>NSAppTransportSecurity</key>
    <dict>
        <key>NSAllowsArbitraryLoads</key>
        <false/>
    </dict>
</dict>
</plist>
