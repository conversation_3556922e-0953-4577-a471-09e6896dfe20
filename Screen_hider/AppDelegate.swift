import Cocoa
import Carbon
import os.log
import UserNotifications

/// Main application delegate that manages the overlay window and menu bar interface
class AppDelegate: NSObject, NSApplicationDelegate {

    // MARK: - Properties

    /// Logger for this class
    private let logger = Logger(subsystem: "com.screenhider.app", category: "AppDelegate")

    /// The overlay window instance
    private var overlayWindow: OverlayWindow?

    /// Status bar item for menu bar interface
    private var statusItem: NSStatusItem?

    /// Menu for the status bar item
    private var menu: NSMenu?

    /// Global hotkey monitor
    private var globalHotkeyMonitor: Any?

    /// User preferences
    private var preferences = UserPreferences()

    /// Current overlay color selection
    private var currentColorSelection: OverlayColor = .black
    
    // MARK: - Application Lifecycle

    func applicationDidFinishLaunching(_ notification: Notification) {
        logger.info("🚀 ScreenHider application starting...")

        // Load user preferences
        preferences.load()
        currentColorSelection = preferences.overlayColor

        // Setup components
        setupStatusBarItem()
        logger.info("✅ Status bar item created")

        setupOverlayWindow()
        logger.info("✅ Overlay window created")

        setupGlobalHotkey()
        logger.info("✅ Global hotkeys setup (Cmd+Shift+H/R/W)")

        // Apply saved preferences
        applyPreferences()

        // Start with overlay in saved state
        if preferences.startWithOverlayVisible {
            showOverlay()
        } else {
            hideOverlay()
        }

        logger.info("🎉 ScreenHider started successfully!")

        // Show welcome notification if first launch
        if preferences.isFirstLaunch {
            showWelcomeNotification()
            preferences.isFirstLaunch = false
            preferences.save()
        }
    }

    func applicationWillTerminate(_ notification: Notification) {
        logger.info("🛑 ScreenHider terminating...")

        // Save current state
        preferences.overlayVisible = overlayWindow?.isOverlayVisible ?? false
        preferences.save()

        // Cleanup
        cleanupGlobalHotkey()
        overlayWindow?.hideOverlay()
        statusItem = nil

        logger.info("✅ Cleanup complete")
    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        // Don't terminate when windows are closed (menu bar app)
        return false
    }
    
    // MARK: - Status Bar Setup

    /// Sets up the status bar item and menu
    private func setupStatusBarItem() {
        statusItem = NSStatusBar.system.statusItem(withLength: NSStatusItem.squareLength)

        guard let statusItem = statusItem else {
            logger.error("❌ Could not create status item")
            return
        }

        // Set the status bar icon and behavior
        if let button = statusItem.button {
            button.image = createStatusBarIcon()
            button.toolTip = "ScreenHider - Right-click for menu, left-click to toggle"
            button.target = self
            button.action = #selector(statusBarButtonClicked(_:))
            button.sendAction(on: [.leftMouseUp, .rightMouseUp])
        }

        // Create and set up the menu
        setupMenu()

        logger.debug("✅ Status bar item configured")
    }

    /// Handles status bar button clicks
    @objc private func statusBarButtonClicked(_ sender: NSStatusBarButton) {
        guard let event = NSApp.currentEvent else { return }

        if event.type == .rightMouseUp {
            // Right-click: show menu
            statusItem?.menu = menu
            statusItem?.button?.performClick(nil)
            statusItem?.menu = nil
        } else {
            // Left-click: toggle overlay
            toggleOverlay()
        }
    }
    
    /// Creates an enhanced icon for the status bar
    private func createStatusBarIcon() -> NSImage {
        let image = NSImage(size: NSSize(width: 18, height: 18))
        image.lockFocus()

        // Draw screen representation
        let screenRect = NSRect(x: 1, y: 3, width: 16, height: 12)
        NSColor.controlTextColor.withAlphaComponent(0.8).setStroke()
        let screenPath = NSBezierPath(rect: screenRect)
        screenPath.lineWidth = 1.0
        screenPath.stroke()

        // Draw the masked area (right quarter)
        let maskedRect = NSRect(x: 13, y: 4, width: 3, height: 10)
        let overlayColor = overlayWindow?.isOverlayVisible == true ? NSColor.systemRed : NSColor.controlTextColor
        overlayColor.setFill()
        maskedRect.fill()

        // Add small indicator dots
        if overlayWindow?.isOverlayVisible == true {
            NSColor.systemGreen.setFill()
            let indicatorRect = NSRect(x: 15, y: 1, width: 2, height: 2)
            NSBezierPath(ovalIn: indicatorRect).fill()
        }

        image.unlockFocus()
        image.isTemplate = true
        return image
    }

    /// Updates the status bar icon to reflect current state
    private func updateStatusBarIcon() {
        statusItem?.button?.image = createStatusBarIcon()
    }
    
    /// Sets up the enhanced status bar menu
    private func setupMenu() {
        menu = NSMenu()
        menu?.autoenablesItems = false

        // Toggle overlay item with keyboard shortcut
        let toggleItem = NSMenuItem(
            title: "Show Overlay",
            action: #selector(toggleOverlay),
            keyEquivalent: "h"
        )
        toggleItem.keyEquivalentModifierMask = [.command, .shift]
        toggleItem.target = self
        menu?.addItem(toggleItem)

        menu?.addItem(NSMenuItem.separator())

        // Resize mode toggle
        let resizeModeItem = NSMenuItem(
            title: "Resize Mode",
            action: #selector(toggleResizeMode),
            keyEquivalent: "r"
        )
        resizeModeItem.keyEquivalentModifierMask = [.command, .shift]
        resizeModeItem.target = self
        menu?.addItem(resizeModeItem)

        // Workspace mode toggle
        let workspaceModeItem = NSMenuItem(
            title: "Workspace Mode",
            action: #selector(toggleWorkspaceMode),
            keyEquivalent: "w"
        )
        workspaceModeItem.keyEquivalentModifierMask = [.command, .shift]
        workspaceModeItem.target = self
        menu?.addItem(workspaceModeItem)

        menu?.addItem(NSMenuItem.separator())

        // Color options submenu
        setupColorSubmenu()

        // Opacity submenu
        setupOpacitySubmenu()

        menu?.addItem(NSMenuItem.separator())

        // Preferences item
        let preferencesItem = NSMenuItem(
            title: "Preferences...",
            action: #selector(showPreferences),
            keyEquivalent: ","
        )
        preferencesItem.target = self
        menu?.addItem(preferencesItem)

        menu?.addItem(NSMenuItem.separator())

        // Help item
        let helpItem = NSMenuItem(
            title: "Help",
            action: #selector(showHelp),
            keyEquivalent: "?"
        )
        helpItem.target = self
        menu?.addItem(helpItem)

        // About item
        let aboutItem = NSMenuItem(
            title: "About ScreenHider",
            action: #selector(showAbout),
            keyEquivalent: ""
        )
        aboutItem.target = self
        menu?.addItem(aboutItem)

        menu?.addItem(NSMenuItem.separator())

        // Quit item
        let quitItem = NSMenuItem(
            title: "Quit ScreenHider",
            action: #selector(quitApplication),
            keyEquivalent: "q"
        )
        quitItem.target = self
        menu?.addItem(quitItem)

        logger.debug("✅ Menu setup complete")
    }
    
    // MARK: - Overlay Management

    /// Sets up the overlay window
    private func setupOverlayWindow() {
        guard let mainScreen = NSScreen.main else {
            logger.error("❌ Could not get main screen")
            return
        }

        overlayWindow = OverlayWindow(
            contentRect: mainScreen.frame,
            styleMask: .borderless,
            backing: .buffered,
            defer: false
        )

        logger.debug("✅ Overlay window created and configured")
    }

    /// Shows the overlay window
    private func showOverlay() {
        logger.info("🔴 Showing overlay...")
        overlayWindow?.showOverlay()
        updateMenuItemTitles()
        updateStatusBarIcon()
        logger.info("✅ Overlay is now visible")
    }

    /// Hides the overlay window
    private func hideOverlay() {
        logger.info("⚫ Hiding overlay...")
        overlayWindow?.hideOverlay()
        updateMenuItemTitles()
        updateStatusBarIcon()
        logger.info("✅ Overlay is now hidden")
    }

    /// Updates menu item titles based on current state
    private func updateMenuItemTitles() {
        guard let menu = menu else { return }

        let isVisible = overlayWindow?.isOverlayVisible ?? false
        if let toggleItem = menu.item(at: 0) {
            toggleItem.title = isVisible ? "Hide Overlay" : "Show Overlay"
        }

        // Update resize mode item
        let isResizeMode = overlayWindow?.isResizeMode ?? false
        if let resizeModeItem = menu.item(withTitle: "Resize Mode") ?? menu.item(withTitle: "Exit Resize Mode") {
            resizeModeItem.title = isResizeMode ? "Exit Resize Mode" : "Resize Mode"
        }

        // Update workspace mode item
        let isWorkspaceMode = overlayWindow?.isWorkspaceMode ?? false
        if let workspaceModeItem = menu.item(withTitle: "Workspace Mode") ?? menu.item(withTitle: "Exit Workspace Mode") {
            workspaceModeItem.title = isWorkspaceMode ? "Exit Workspace Mode" : "Workspace Mode"
        }
    }
    
    // MARK: - Menu Actions

    @objc private func toggleOverlay() {
        overlayWindow?.toggleOverlay()
        updateMenuItemTitles()
        updateStatusBarIcon()

        // Save state
        preferences.overlayVisible = overlayWindow?.isOverlayVisible ?? false
        preferences.save()

        logger.info("🔄 Overlay toggled")
    }

    @objc private func toggleResizeMode() {
        overlayWindow?.toggleResizeMode()
        updateMenuItemTitles()
        logger.info("🔧 Resize mode toggled")
    }

    @objc private func toggleWorkspaceMode() {
        overlayWindow?.toggleWorkspaceMode()
        updateMenuItemTitles()
        logger.info("💼 Workspace mode toggled")
    }
    
    @objc private func showAbout() {
        let alert = NSAlert()
        alert.messageText = "ScreenHider"
        alert.informativeText = """
        Version 1.0
        
        A macOS application that creates a screen overlay to hide the right quarter of your screen from screen sharing applications while maintaining full local interaction.
        
        Features:
        • Excludes overlay from screen capture and sharing
        • Transparent to mouse and keyboard input
        • Works across all Spaces and full-screen apps
        • Configurable overlay colors
        
        Compatible with macOS 11.0 and above.
        """
        alert.alertStyle = .informational
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }
    
    @objc private func quitApplication() {
        NSApplication.shared.terminate(self)
    }

    // MARK: - Missing Method Implementations

    /// Sets up color submenu
    private func setupColorSubmenu() {
        let colorSubmenu = NSMenu()

        for color in OverlayColor.allCases {
            let item = NSMenuItem(title: color.displayName, action: #selector(colorSelected(_:)), keyEquivalent: "")
            item.target = self
            item.tag = color.rawValue
            item.state = (color == currentColorSelection) ? .on : .off
            colorSubmenu.addItem(item)
        }

        let colorMenuItem = NSMenuItem(title: "Overlay Color", action: nil, keyEquivalent: "")
        colorMenuItem.submenu = colorSubmenu
        menu?.addItem(colorMenuItem)
    }

    /// Sets up opacity submenu
    private func setupOpacitySubmenu() {
        let opacitySubmenu = NSMenu()
        let opacityValues: [(String, CGFloat)] = [
            ("100%", 1.0),
            ("90%", 0.9),
            ("80%", 0.8),
            ("70%", 0.7),
            ("60%", 0.6)
        ]

        for (title, value) in opacityValues {
            let item = NSMenuItem(title: title, action: #selector(opacitySelected(_:)), keyEquivalent: "")
            item.target = self
            item.representedObject = value
            opacitySubmenu.addItem(item)
        }

        let opacityMenuItem = NSMenuItem(title: "Opacity", action: nil, keyEquivalent: "")
        opacityMenuItem.submenu = opacitySubmenu
        menu?.addItem(opacityMenuItem)
    }

    @objc private func colorSelected(_ sender: NSMenuItem) {
        guard let color = OverlayColor(rawValue: sender.tag) else { return }
        setOverlayColor(color)
    }

    @objc private func opacitySelected(_ sender: NSMenuItem) {
        guard let opacity = sender.representedObject as? CGFloat else { return }
        overlayWindow?.updateOverlayOpacity(opacity)
        preferences.overlayOpacity = opacity
        preferences.save()
    }

    @objc private func showPreferences() {
        // TODO: Implement preferences window
        logger.info("Preferences requested")
    }

    @objc private func showHelp() {
        let alert = NSAlert()
        alert.messageText = "ScreenHider Help"
        alert.informativeText = """
        Quick Start:
        • Left-click the menu bar icon to toggle overlay
        • Right-click for full menu options
        • Use Cmd+Shift+H to toggle overlay
        • Use Cmd+Shift+R to enter resize mode
        • Use Cmd+Shift+W to enter workspace mode

        Resize Mode:
        • Drag the overlay to reposition it
        • Drag corners/edges to resize
        • Exit resize mode when done

        Workspace Mode:
        • Opens a private web browser within the overlay
        • Access Google Docs, websites, or any URL
        • Content is hidden from screen sharing
        • Perfect for private notes, research, or work

        The overlay hides selected areas from screen sharing while keeping them fully interactive for you.
        """
        alert.alertStyle = .informational
        alert.addButton(withTitle: "OK")
        alert.runModal()
    }

    // MARK: - Additional Methods

    /// Applies saved preferences to the overlay
    private func applyPreferences() {
        setOverlayColor(preferences.overlayColor)
        overlayWindow?.updateOverlayOpacity(preferences.overlayOpacity)
    }

    /// Sets the overlay color
    private func setOverlayColor(_ color: OverlayColor) {
        currentColorSelection = color
        overlayWindow?.updateOverlayColor(color.nsColor)
        preferences.overlayColor = color
        preferences.save()
        updateColorMenuSelection()
        updateStatusBarIcon()
        logger.info("🎨 Overlay color changed to: \(color.displayName)")
    }

    /// Updates color menu selection state
    private func updateColorMenuSelection() {
        guard let menu = menu,
              let colorMenuItem = menu.item(withTitle: "Overlay Color"),
              let colorSubmenu = colorMenuItem.submenu else { return }

        for item in colorSubmenu.items {
            item.state = (item.tag == currentColorSelection.rawValue) ? .on : .off
        }
    }

    /// Shows welcome notification for first launch
    private func showWelcomeNotification() {
        // Use modern UserNotifications framework for macOS 11.0+
        if #available(macOS 11.0, *) {
            let center = UNUserNotificationCenter.current()
            center.requestAuthorization(options: [.alert, .sound]) { granted, error in
                if granted {
                    let content = UNMutableNotificationContent()
                    content.title = "ScreenHider Started"
                    content.body = "Click the menu bar icon to toggle overlay. Use Cmd+Shift+H for quick toggle."
                    content.sound = .default

                    let request = UNNotificationRequest(identifier: "welcome", content: content, trigger: nil)
                    center.add(request)
                }
            }
        } else {
            // Fallback for older macOS versions (though we require 11.0+)
            logger.info("Welcome notification skipped - UserNotifications not available")
        }
    }

    /// Sets up global hotkey monitoring
    private func setupGlobalHotkey() {
        globalHotkeyMonitor = NSEvent.addGlobalMonitorForEvents(matching: .keyDown) { [weak self] event in
            if event.modifierFlags.contains([.command, .shift]) {
                switch event.keyCode {
                case 4: // 'H' key
                    DispatchQueue.main.async {
                        self?.toggleOverlay()
                    }
                case 15: // 'R' key
                    DispatchQueue.main.async {
                        self?.toggleResizeMode()
                    }
                case 13: // 'W' key
                    DispatchQueue.main.async {
                        self?.toggleWorkspaceMode()
                    }
                default:
                    break
                }
            }
        }
    }

    /// Cleans up global hotkey monitoring
    private func cleanupGlobalHotkey() {
        if let monitor = globalHotkeyMonitor {
            NSEvent.removeMonitor(monitor)
            globalHotkeyMonitor = nil
        }
    }

    /// Legacy color methods for compatibility
    @objc private func setBlackColor() { setOverlayColor(.black) }
    @objc private func setGrayColor() { setOverlayColor(.darkGray) }
    @objc private func setRedColor() { setOverlayColor(.darkRed) }
}

// MARK: - Supporting Types

/// Enumeration of available overlay colors
enum OverlayColor: Int, CaseIterable {
    case black = 0
    case darkGray = 1
    case darkRed = 2
    case darkBlue = 3
    case darkGreen = 4

    var displayName: String {
        switch self {
        case .black: return "Black"
        case .darkGray: return "Dark Gray"
        case .darkRed: return "Dark Red"
        case .darkBlue: return "Dark Blue"
        case .darkGreen: return "Dark Green"
        }
    }

    var nsColor: NSColor {
        switch self {
        case .black: return .black
        case .darkGray: return .darkGray
        case .darkRed: return NSColor.systemRed.withAlphaComponent(0.8)
        case .darkBlue: return NSColor.systemBlue.withAlphaComponent(0.8)
        case .darkGreen: return NSColor.systemGreen.withAlphaComponent(0.8)
        }
    }
}

/// User preferences management
class UserPreferences {
    private let userDefaults = UserDefaults.standard

    // Keys
    private enum Keys {
        static let overlayColor = "overlayColor"
        static let overlayOpacity = "overlayOpacity"
        static let overlayVisible = "overlayVisible"
        static let startWithOverlayVisible = "startWithOverlayVisible"
        static let isFirstLaunch = "isFirstLaunch"
    }

    // Properties
    var overlayColor: OverlayColor {
        get { OverlayColor(rawValue: userDefaults.integer(forKey: Keys.overlayColor)) ?? .black }
        set { userDefaults.set(newValue.rawValue, forKey: Keys.overlayColor) }
    }

    var overlayOpacity: CGFloat {
        get {
            let value = userDefaults.double(forKey: Keys.overlayOpacity)
            return value == 0 ? 0.7 : value
        }
        set { userDefaults.set(newValue, forKey: Keys.overlayOpacity) }
    }

    var overlayVisible: Bool {
        get { userDefaults.bool(forKey: Keys.overlayVisible) }
        set { userDefaults.set(newValue, forKey: Keys.overlayVisible) }
    }

    var startWithOverlayVisible: Bool {
        get { userDefaults.bool(forKey: Keys.startWithOverlayVisible) }
        set { userDefaults.set(newValue, forKey: Keys.startWithOverlayVisible) }
    }

    var isFirstLaunch: Bool {
        get {
            if userDefaults.object(forKey: Keys.isFirstLaunch) == nil {
                return true
            }
            return userDefaults.bool(forKey: Keys.isFirstLaunch)
        }
        set { userDefaults.set(newValue, forKey: Keys.isFirstLaunch) }
    }

    func load() {
        // Preferences are loaded automatically via UserDefaults
    }

    func save() {
        userDefaults.synchronize()
    }
}
