import Cocoa
import os.log

/// A specialized NSWindow that creates a screen overlay for masking content
/// while remaining transparent to input and excluded from screen capture
class OverlayWindow: NSWindow {

    // MARK: - Properties

    /// Logger for this class
    private let logger = Logger(subsystem: "com.screenhider.app", category: "OverlayWindow")

    /// The color of the overlay (default: black)
    var overlayColor: NSColor = .black {
        didSet {
            updateOverlayAppearance()
        }
    }

    /// The opacity of the overlay (0.0 to 1.0)
    var overlayOpacity: CGFloat = 0.7 {
        didSet {
            alphaValue = overlayOpacity
        }
    }

    /// Whether the overlay is currently visible
    private(set) var isOverlayVisible: Bool = false

    /// Whether the overlay is in resize mode (shows resize handles)
    private(set) var isResizeMode: Bool = false

    /// Screen change observer token
    private var screenChangeObserver: NSObjectProtocol?
    
    // MARK: - Initialization

    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: contentRect, styleMask: style, backing: backingStoreType, defer: flag)
        logger.info("🪟 Initializing OverlayWindow")
        setupOverlayWindow()
    }

    /// Configures the window with all necessary properties for screen masking
    private func setupOverlayWindow() {
        logger.debug("⚙️ Setting up overlay window configuration")

        // Basic window configuration
        self.styleMask = .borderless
        self.backgroundColor = overlayColor
        self.isOpaque = false
        self.hasShadow = false
        self.ignoresMouseEvents = false  // Allow mouse events for dragging/resizing
        self.alphaValue = overlayOpacity

        // Window level - use screenSaver level to stay above most content
        // but below system UI elements like notifications
        self.level = .screenSaver

        // Collection behavior for proper display across spaces and full screen
        self.collectionBehavior = [
            .canJoinAllSpaces,           // Appears on all Spaces
            .fullScreenAuxiliary,        // Appears over full screen apps
            .stationary,                 // Doesn't participate in Exposé
            .ignoresCycle               // Not included in window cycling
        ]

        // Critical: Exclude from screen capture and sharing
        if #available(macOS 11.0, *) {
            self.sharingType = .none
            logger.info("✅ Screen capture exclusion enabled (sharingType = .none)")
        } else {
            logger.warning("⚠️ Screen capture exclusion not available on this macOS version")
        }

        // Make sure the window doesn't appear in mission control
        self.isExcludedFromWindowsMenu = true

        // Set up the content view
        setupContentView()

        // Set up screen change monitoring
        setupScreenChangeObservers()

        logger.info("✅ Overlay window setup complete")
    }
    
    // MARK: - Content View Setup

    /// Sets up the content view with the overlay color
    private func setupContentView() {
        let contentView = OverlayContentView(frame: self.contentRect(forFrameRect: self.frame))
        contentView.overlayColor = overlayColor
        self.contentView = contentView
        logger.debug("✅ Content view setup complete")
    }

    /// Updates the overlay appearance
    private func updateOverlayAppearance() {
        backgroundColor = overlayColor
        if let overlayContentView = contentView as? OverlayContentView {
            overlayContentView.overlayColor = overlayColor
        }
        display()
    }

    /// Updates the overlay color and refreshes the display
    func updateOverlayColor(_ color: NSColor) {
        logger.info("🎨 Updating overlay color to: \(color)")
        overlayColor = color
    }

    /// Updates the overlay opacity
    func updateOverlayOpacity(_ opacity: CGFloat) {
        logger.info("🔍 Updating overlay opacity to: \(opacity)")
        overlayOpacity = max(0.0, min(1.0, opacity))
    }

    /// Toggles resize mode on/off
    func toggleResizeMode() {
        isResizeMode.toggle()
        updateInteractionMode()
        logger.info("🔧 Resize mode: \(self.isResizeMode ? "ON" : "OFF")")
    }

    /// Updates interaction mode based on resize state
    private func updateInteractionMode() {
        if isResizeMode {
            // In resize mode: allow interaction for dragging/resizing
            self.ignoresMouseEvents = false
            // Keep the same transparency so you can see through it
            self.backgroundColor = overlayColor
            self.alphaValue = overlayOpacity

            // Add visual indicators (border and handles)
            self.hasShadow = true
            if let contentView = self.contentView as? OverlayContentView {
                contentView.showResizeHandles = true
                contentView.needsDisplay = true
            }
        } else {
            // In normal mode: click-through for privacy
            self.ignoresMouseEvents = true
            self.backgroundColor = overlayColor
            self.alphaValue = overlayOpacity

            // Remove visual indicators
            self.hasShadow = false
            if let contentView = self.contentView as? OverlayContentView {
                contentView.showResizeHandles = false
                contentView.needsDisplay = true
            }
        }
    }
    
    // MARK: - Positioning and Visibility

    /// Positions the window to cover the right 1/4 of the main screen
    func positionForRightQuarter() {
        guard let overlayFrame = ScreenManager.shared.rightQuarterFrame() else {
            logger.error("❌ Could not calculate right quarter frame")
            return
        }

        setFrame(overlayFrame, display: true)
        logger.info("📍 Positioned overlay at: \(NSStringFromRect(overlayFrame))")
    }

    /// Shows the overlay window
    func showOverlay() {
        guard !isOverlayVisible else {
            logger.debug("Overlay already visible, skipping")
            return
        }

        positionForRightQuarter()
        orderFront(nil)
        isOverlayVisible = true
        logger.info("👁️ Overlay is now visible")
    }

    /// Hides the overlay window
    func hideOverlay() {
        guard isOverlayVisible else {
            logger.debug("Overlay already hidden, skipping")
            return
        }

        orderOut(nil)
        isOverlayVisible = false
        logger.info("🙈 Overlay is now hidden")
    }

    /// Toggles the overlay visibility
    func toggleOverlay() {
        if isOverlayVisible {
            hideOverlay()
        } else {
            showOverlay()
        }
    }
    
    // MARK: - Window Behavior Overrides

    /// Override to prevent the window from being moved manually
    override func setFrameOrigin(_ point: NSPoint) {
        // Prevent manual repositioning - maintain current position
        super.setFrameOrigin(frame.origin)
        logger.debug("🚫 Prevented manual window repositioning")
    }

    /// Override to allow only programmatic frame changes
    override func setFrame(_ frameRect: NSRect, display flag: Bool) {
        // Allow programmatic frame changes for positioning
        super.setFrame(frameRect, display: flag)
    }

    /// Override to prevent the window from becoming key
    override var canBecomeKey: Bool {
        return false
    }

    /// Override to prevent the window from becoming main
    override var canBecomeMain: Bool {
        return false
    }

    /// Override to prevent the window from accepting first responder
    override var acceptsFirstResponder: Bool {
        return false
    }

    // MARK: - Cleanup

    /// Removes observers when the window is deallocated
    deinit {
        logger.info("🗑️ Cleaning up OverlayWindow")
        if let observer = screenChangeObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Screen Change Monitoring

extension OverlayWindow {

    /// Sets up observers for screen configuration changes
    func setupScreenChangeObservers() {
        logger.debug("📺 Setting up screen change observers")

        screenChangeObserver = NotificationCenter.default.addObserver(
            forName: NSApplication.didChangeScreenParametersNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleScreenConfigurationChange()
        }
    }

    /// Handles screen configuration changes by repositioning the overlay
    private func handleScreenConfigurationChange() {
        logger.info("📺 Screen configuration changed, repositioning overlay")

        // Add a small delay to ensure screen changes are fully processed
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            guard let self = self else { return }

            if self.isOverlayVisible {
                self.positionForRightQuarter()
                self.logger.debug("✅ Overlay repositioned after screen change")
            }
        }
    }
}

// MARK: - Custom Content View

/// Custom NSView for the overlay content with enhanced drawing
class OverlayContentView: NSView {

    /// The overlay color
    var overlayColor: NSColor = .black {
        didSet {
            needsDisplay = true
        }
    }

    /// Whether to show resize handles
    var showResizeHandles: Bool = false {
        didSet {
            needsDisplay = true
        }
    }

    /// Tracking area for mouse events
    private var trackingArea: NSTrackingArea?

    /// Current mouse position for dragging
    private var lastMouseLocation: NSPoint = .zero

    /// Whether we're currently dragging
    private var isDragging: Bool = false

    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        wantsLayer = true
        layer?.backgroundColor = overlayColor.cgColor
        setupTrackingArea()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        wantsLayer = true
        layer?.backgroundColor = overlayColor.cgColor
        setupTrackingArea()
    }

    /// Sets up mouse tracking
    private func setupTrackingArea() {
        trackingArea = NSTrackingArea(
            rect: bounds,
            options: [.activeAlways, .mouseEnteredAndExited, .mouseMoved],
            owner: self,
            userInfo: nil
        )
        addTrackingArea(trackingArea!)
    }

    override func updateTrackingAreas() {
        super.updateTrackingAreas()
        if let trackingArea = trackingArea {
            removeTrackingArea(trackingArea)
        }
        setupTrackingArea()
    }

    override func draw(_ dirtyRect: NSRect) {
        super.draw(dirtyRect)

        // Fill the entire view with the overlay color
        overlayColor.setFill()
        dirtyRect.fill()

        // Draw resize handles if in resize mode
        if showResizeHandles {
            drawResizeHandles()
            drawBorder()
        }
    }

    /// Draws resize handles at corners and edges
    private func drawResizeHandles() {
        // Use bright colors that are visible but not too intrusive
        NSColor.systemYellow.withAlphaComponent(0.8).setFill()
        NSColor.systemOrange.setStroke()

        let handleSize: CGFloat = 6  // Smaller handles
        let handles = [
            // Just corners for simplicity
            NSRect(x: 2, y: 2, width: handleSize, height: handleSize), // Bottom-left
            NSRect(x: bounds.width - handleSize - 2, y: 2, width: handleSize, height: handleSize), // Bottom-right
            NSRect(x: 2, y: bounds.height - handleSize - 2, width: handleSize, height: handleSize), // Top-left
            NSRect(x: bounds.width - handleSize - 2, y: bounds.height - handleSize - 2, width: handleSize, height: handleSize), // Top-right
        ]

        for handle in handles {
            let path = NSBezierPath(ovalIn: handle)  // Make them circular
            path.fill()
            path.lineWidth = 1
            path.stroke()
        }
    }

    /// Draws a border around the overlay
    private func drawBorder() {
        NSColor.systemBlue.withAlphaComponent(0.6).setStroke()
        let borderPath = NSBezierPath(rect: bounds.insetBy(dx: 1, dy: 1))
        borderPath.lineWidth = 1
        borderPath.setLineDash([4, 4], count: 2, phase: 0)  // Dashed line
        borderPath.stroke()
    }

    override func updateLayer() {
        super.updateLayer()
        layer?.backgroundColor = overlayColor.cgColor
    }

    // MARK: - Mouse Events

    override func mouseDown(with event: NSEvent) {
        guard showResizeHandles else { return }

        lastMouseLocation = event.locationInWindow
        isDragging = true
    }

    override func mouseDragged(with event: NSEvent) {
        guard showResizeHandles && isDragging else { return }
        guard let window = window else { return }

        let currentLocation = event.locationInWindow
        let deltaX = currentLocation.x - lastMouseLocation.x
        let deltaY = currentLocation.y - lastMouseLocation.y

        // Move the window
        var newFrame = window.frame
        newFrame.origin.x += deltaX
        newFrame.origin.y += deltaY

        window.setFrame(newFrame, display: true)
    }

    override func mouseUp(with event: NSEvent) {
        isDragging = false
    }

    override func mouseMoved(with event: NSEvent) {
        guard showResizeHandles else { return }

        // Update cursor based on position
        let location = event.locationInWindow
        updateCursor(for: location)
    }

    /// Updates cursor based on mouse position
    private func updateCursor(for location: NSPoint) {
        let handleSize: CGFloat = 8
        let edgeThreshold: CGFloat = 10

        // Check if near resize handles - use available cursor types
        if location.x < edgeThreshold && location.y < edgeThreshold {
            NSCursor.arrow.set()  // Bottom-left corner
        } else if location.x > bounds.width - edgeThreshold && location.y < edgeThreshold {
            NSCursor.arrow.set()  // Bottom-right corner
        } else if location.x < edgeThreshold && location.y > bounds.height - edgeThreshold {
            NSCursor.arrow.set()  // Top-left corner
        } else if location.x > bounds.width - edgeThreshold && location.y > bounds.height - edgeThreshold {
            NSCursor.arrow.set()  // Top-right corner
        } else if location.x < edgeThreshold || location.x > bounds.width - edgeThreshold {
            NSCursor.resizeLeftRight.set()
        } else if location.y < edgeThreshold || location.y > bounds.height - edgeThreshold {
            NSCursor.resizeUpDown.set()
        } else {
            NSCursor.openHand.set()
        }
    }

    override func mouseExited(with event: NSEvent) {
        NSCursor.arrow.set()
    }
}
