import Cocoa
import WebKit
import os.log

/// A specialized NSWindow that creates a screen overlay for masking content
/// while remaining transparent to input and excluded from screen capture
class OverlayWindow: NSWindow {

    // MARK: - Properties

    /// Logger for this class
    private let logger = Logger(subsystem: "com.screenhider.app", category: "OverlayWindow")

    /// The color of the overlay (default: black)
    var overlayColor: NSColor = .black {
        didSet {
            updateOverlayAppearance()
        }
    }

    /// The opacity of the overlay (0.0 to 1.0)
    var overlayOpacity: CGFloat = 0.7 {
        didSet {
            alphaValue = overlayOpacity
        }
    }

    /// Whether the overlay is currently visible
    private(set) var isOverlayVisible: Bool = false

    /// Whether the overlay is in resize mode (shows resize handles)
    private(set) var isResizeMode: Bool = false

    /// Whether the overlay is in workspace mode (shows web view and controls)
    private(set) var isWorkspaceMode: Bool = false

    /// Web view for displaying content within the overlay
    private var webView: WKWebView?

    /// Toolbar view for workspace controls
    private var workspaceToolbar: NSView?

    /// Screen change observer token
    private var screenChangeObserver: NSObjectProtocol?
    
    // MARK: - Initialization

    override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: contentRect, styleMask: style, backing: backingStoreType, defer: flag)
        logger.info("🪟 Initializing OverlayWindow")
        setupOverlayWindow()
    }

    /// Configures the window with all necessary properties for screen masking
    private func setupOverlayWindow() {
        logger.debug("⚙️ Setting up overlay window configuration")

        // Basic window configuration - make it resizable like a normal window
        self.styleMask = [.resizable, .titled, .closable, .miniaturizable]
        self.backgroundColor = overlayColor
        self.isOpaque = false
        self.hasShadow = true
        self.ignoresMouseEvents = false  // Allow mouse events for dragging/resizing
        self.alphaValue = overlayOpacity
        self.title = "ScreenHider Overlay"

        // Window level - use screenSaver level to stay above most content
        // but below system UI elements like notifications
        self.level = .screenSaver

        // Collection behavior for proper display across spaces and full screen
        self.collectionBehavior = [
            .canJoinAllSpaces,           // Appears on all Spaces
            .fullScreenAuxiliary,        // Appears over full screen apps
            .stationary,                 // Doesn't participate in Exposé
            .ignoresCycle               // Not included in window cycling
        ]

        // Critical: Exclude from screen capture and sharing
        if #available(macOS 11.0, *) {
            self.sharingType = .none
            logger.info("✅ Screen capture exclusion enabled (sharingType = .none)")
        } else {
            logger.warning("⚠️ Screen capture exclusion not available on this macOS version")
        }

        // Make sure the window doesn't appear in mission control
        self.isExcludedFromWindowsMenu = true

        // Set up the content view
        setupContentView()

        // Set up screen change monitoring
        setupScreenChangeObservers()

        logger.info("✅ Overlay window setup complete")
    }
    
    // MARK: - Content View Setup

    /// Sets up the content view with the overlay color
    private func setupContentView() {
        let contentView = OverlayContentView(frame: self.contentRect(forFrameRect: self.frame))
        contentView.overlayColor = overlayColor
        self.contentView = contentView
        logger.debug("✅ Content view setup complete")
    }

    /// Updates the overlay appearance
    private func updateOverlayAppearance() {
        backgroundColor = overlayColor
        if let overlayContentView = contentView as? OverlayContentView {
            overlayContentView.overlayColor = overlayColor
        }
        display()
    }

    /// Updates the overlay color and refreshes the display
    func updateOverlayColor(_ color: NSColor) {
        logger.info("🎨 Updating overlay color to: \(color)")
        overlayColor = color
    }

    /// Updates the overlay opacity
    func updateOverlayOpacity(_ opacity: CGFloat) {
        logger.info("🔍 Updating overlay opacity to: \(opacity)")
        overlayOpacity = max(0.0, min(1.0, opacity))
    }

    /// Sets up workspace content (web view and toolbar)
    private func setupWorkspaceContent() {
        guard webView == nil else { return }  // Already setup

        // Create toolbar
        workspaceToolbar = NSView(frame: NSRect(x: 0, y: contentView!.frame.height - 40, width: contentView!.frame.width, height: 40))
        workspaceToolbar!.wantsLayer = true
        workspaceToolbar!.layer?.backgroundColor = NSColor.controlBackgroundColor.cgColor

        // Create URL text field
        let urlField = NSTextField(frame: NSRect(x: 10, y: 8, width: contentView!.frame.width - 120, height: 24))
        urlField.placeholderString = "Enter URL (e.g., docs.google.com)"
        urlField.target = self
        urlField.action = #selector(urlFieldAction(_:))
        workspaceToolbar!.addSubview(urlField)

        // Create Go button
        let goButton = NSButton(frame: NSRect(x: contentView!.frame.width - 100, y: 8, width: 80, height: 24))
        goButton.title = "Go"
        goButton.target = self
        goButton.action = #selector(goButtonAction(_:))
        workspaceToolbar!.addSubview(goButton)

        // Create web view
        let webViewFrame = NSRect(x: 0, y: 0, width: contentView!.frame.width, height: contentView!.frame.height - 40)
        webView = WKWebView(frame: webViewFrame)
        webView!.autoresizingMask = [.width, .height]

        // Add to content view
        contentView!.addSubview(workspaceToolbar!)
        contentView!.addSubview(webView!)

        // Load default page
        if let url = URL(string: "https://docs.google.com") {
            webView!.load(URLRequest(url: url))
        }

        logger.info("💼 Workspace content setup complete")
    }

    /// Removes workspace content
    private func removeWorkspaceContent() {
        webView?.removeFromSuperview()
        workspaceToolbar?.removeFromSuperview()
        webView = nil
        workspaceToolbar = nil

        // Restore original content view
        setupContentView()
    }

    @objc private func urlFieldAction(_ sender: NSTextField) {
        let urlString = sender.stringValue
        if !urlString.isEmpty {
            openURL(urlString.hasPrefix("http") ? urlString : "https://\(urlString)")
        }
    }

    @objc private func goButtonAction(_ sender: NSButton) {
        if let urlField = workspaceToolbar?.subviews.first(where: { $0 is NSTextField }) as? NSTextField {
            urlFieldAction(urlField)
        }
    }

    /// Toggles resize mode on/off
    func toggleResizeMode() {
        isResizeMode.toggle()
        updateInteractionMode()
        logger.info("🔧 Resize mode: \(self.isResizeMode ? "ON" : "OFF")")
    }

    /// Toggles workspace mode on/off
    func toggleWorkspaceMode() {
        isWorkspaceMode.toggle()
        updateInteractionMode()
        logger.info("💼 Workspace mode: \(self.isWorkspaceMode ? "ON" : "OFF")")
    }

    /// Opens a URL in the workspace web view
    func openURL(_ url: URL) {
        if !isWorkspaceMode {
            toggleWorkspaceMode()
        }

        if let webView = webView {
            let request = URLRequest(url: url)
            webView.load(request)
            logger.info("🌐 Loading URL: \(url.absoluteString)")
        }
    }

    /// Opens a URL from string in the workspace web view
    func openURL(_ urlString: String) {
        guard let url = URL(string: urlString) else {
            logger.error("❌ Invalid URL: \(urlString)")
            return
        }
        openURL(url)
    }

    /// Updates interaction mode based on resize and workspace state
    private func updateInteractionMode() {
        if isWorkspaceMode {
            // In workspace mode: show window with web view and controls
            self.styleMask = [.resizable, .titled, .closable, .miniaturizable]
            self.ignoresMouseEvents = false
            self.backgroundColor = NSColor.windowBackgroundColor
            self.alphaValue = 0.95  // Slightly more opaque for better readability
            self.hasShadow = true
            self.title = "ScreenHider Workspace"

            // Lower window level for normal interaction
            self.level = .normal

            // Make the window key so it can be interacted with
            self.makeKeyAndOrderFront(nil)

            // Setup workspace content
            setupWorkspaceContent()

        } else if isResizeMode {
            // In resize mode: show window frame with resize handles
            self.styleMask = [.resizable, .titled, .closable, .miniaturizable]
            self.ignoresMouseEvents = false
            self.backgroundColor = overlayColor
            self.alphaValue = overlayOpacity
            self.hasShadow = true
            self.title = "ScreenHider - Drag to Move, Resize Edges"

            // Lower window level for normal interaction
            self.level = .normal

            // Make the window key so it can be resized
            self.makeKeyAndOrderFront(nil)

            // Remove workspace content and show overlay
            removeWorkspaceContent()

        } else {
            // In normal mode: borderless and click-through for privacy
            self.styleMask = .borderless
            self.ignoresMouseEvents = true
            self.backgroundColor = overlayColor
            self.alphaValue = overlayOpacity
            self.hasShadow = false

            // Restore high window level for overlay behavior
            self.level = .screenSaver

            // Remove workspace content
            removeWorkspaceContent()
        }
    }
    
    // MARK: - Positioning and Visibility

    /// Positions the window to cover the right 1/4 of the main screen
    func positionForRightQuarter() {
        guard let overlayFrame = ScreenManager.shared.rightQuarterFrame() else {
            logger.error("❌ Could not calculate right quarter frame")
            return
        }

        setFrame(overlayFrame, display: true)
        logger.info("📍 Positioned overlay at: \(NSStringFromRect(overlayFrame))")
    }

    /// Shows the overlay window
    func showOverlay() {
        guard !isOverlayVisible else {
            logger.debug("Overlay already visible, skipping")
            return
        }

        positionForRightQuarter()
        orderFront(nil)
        isOverlayVisible = true
        logger.info("👁️ Overlay is now visible")
    }

    /// Hides the overlay window
    func hideOverlay() {
        guard isOverlayVisible else {
            logger.debug("Overlay already hidden, skipping")
            return
        }

        orderOut(nil)
        isOverlayVisible = false
        logger.info("🙈 Overlay is now hidden")
    }

    /// Toggles the overlay visibility
    func toggleOverlay() {
        if isOverlayVisible {
            hideOverlay()
        } else {
            showOverlay()
        }
    }
    
    // MARK: - Window Behavior Overrides

    /// Override to prevent the window from being moved manually
    override func setFrameOrigin(_ point: NSPoint) {
        // Prevent manual repositioning - maintain current position
        super.setFrameOrigin(frame.origin)
        logger.debug("🚫 Prevented manual window repositioning")
    }

    /// Override to allow only programmatic frame changes
    override func setFrame(_ frameRect: NSRect, display flag: Bool) {
        // Allow programmatic frame changes for positioning
        super.setFrame(frameRect, display: flag)
    }

    /// Override to allow the window to become key when in resize or workspace mode
    override var canBecomeKey: Bool {
        return isResizeMode || isWorkspaceMode
    }

    /// Override to allow the window to become main when in resize or workspace mode
    override var canBecomeMain: Bool {
        return isResizeMode || isWorkspaceMode
    }

    /// Override to allow the window to accept first responder when in resize or workspace mode
    override var acceptsFirstResponder: Bool {
        return isResizeMode || isWorkspaceMode
    }

    /// Override close button to exit current mode instead of closing
    override func performClose(_ sender: Any?) {
        if isWorkspaceMode {
            toggleWorkspaceMode()  // Exit workspace mode instead of closing
        } else if isResizeMode {
            toggleResizeMode()  // Exit resize mode instead of closing
        } else {
            // In normal mode, hide the overlay
            hideOverlay()
        }
    }

    /// Override miniaturize to prevent minimizing
    override func performMiniaturize(_ sender: Any?) {
        // Do nothing - prevent minimizing
    }

    // MARK: - Cleanup

    /// Removes observers when the window is deallocated
    deinit {
        logger.info("🗑️ Cleaning up OverlayWindow")
        if let observer = screenChangeObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - Screen Change Monitoring

extension OverlayWindow {

    /// Sets up observers for screen configuration changes
    func setupScreenChangeObservers() {
        logger.debug("📺 Setting up screen change observers")

        screenChangeObserver = NotificationCenter.default.addObserver(
            forName: NSApplication.didChangeScreenParametersNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            self?.handleScreenConfigurationChange()
        }
    }

    /// Handles screen configuration changes by repositioning the overlay
    private func handleScreenConfigurationChange() {
        logger.info("📺 Screen configuration changed, repositioning overlay")

        // Add a small delay to ensure screen changes are fully processed
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            guard let self = self else { return }

            if self.isOverlayVisible {
                self.positionForRightQuarter()
                self.logger.debug("✅ Overlay repositioned after screen change")
            }
        }
    }
}

// MARK: - Custom Content View

/// Custom NSView for the overlay content with enhanced drawing
class OverlayContentView: NSView {

    /// The overlay color
    var overlayColor: NSColor = .black {
        didSet {
            needsDisplay = true
        }
    }

    /// Whether to show resize handles (not used with native resizing)
    var showResizeHandles: Bool = false {
        didSet {
            needsDisplay = true
        }
    }

    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        wantsLayer = true
        layer?.backgroundColor = overlayColor.cgColor
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        wantsLayer = true
        layer?.backgroundColor = overlayColor.cgColor
    }

    override func draw(_ dirtyRect: NSRect) {
        super.draw(dirtyRect)

        // Fill the entire view with the overlay color
        overlayColor.setFill()
        dirtyRect.fill()

        // Note: No need for custom resize handles since we use native window resizing
    }

    /// Draws resize handles at corners and edges
    private func drawResizeHandles() {
        // Use bright colors that are visible but not too intrusive
        NSColor.systemYellow.withAlphaComponent(0.8).setFill()
        NSColor.systemOrange.setStroke()

        let handleSize: CGFloat = 6  // Smaller handles
        let handles = [
            // Just corners for simplicity
            NSRect(x: 2, y: 2, width: handleSize, height: handleSize), // Bottom-left
            NSRect(x: bounds.width - handleSize - 2, y: 2, width: handleSize, height: handleSize), // Bottom-right
            NSRect(x: 2, y: bounds.height - handleSize - 2, width: handleSize, height: handleSize), // Top-left
            NSRect(x: bounds.width - handleSize - 2, y: bounds.height - handleSize - 2, width: handleSize, height: handleSize), // Top-right
        ]

        for handle in handles {
            let path = NSBezierPath(ovalIn: handle)  // Make them circular
            path.fill()
            path.lineWidth = 1
            path.stroke()
        }
    }

    /// Draws a border around the overlay
    private func drawBorder() {
        NSColor.systemBlue.withAlphaComponent(0.6).setStroke()
        let borderPath = NSBezierPath(rect: bounds.insetBy(dx: 1, dy: 1))
        borderPath.lineWidth = 1
        borderPath.setLineDash([4, 4], count: 2, phase: 0)  // Dashed line
        borderPath.stroke()
    }

    override func updateLayer() {
        super.updateLayer()
        layer?.backgroundColor = overlayColor.cgColor
    }

    // MARK: - Mouse Events
    // Note: Using native window resizing, so no custom mouse handling needed
}
